import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/chat_service.dart';

class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatService();
  
  List<Conversation> _conversations = [];
  Conversation? _currentConversation;
  bool _isLoading = false;
  bool _isTyping = false;
  String? _error;
  Message? _streamingMessage;

  // Getters
  List<Conversation> get conversations => _conversations;
  Conversation? get currentConversation => _currentConversation;
  bool get isLoading => _isLoading;
  bool get isTyping => _isTyping;
  String? get error => _error;
  Message? get streamingMessage => _streamingMessage;

  // Initialize provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await loadConversations();
    } catch (e) {
      _setError('Failed to initialize chat: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load all conversations
  Future<void> loadConversations() async {
    try {
      _conversations = await _chatService.getConversations();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load conversations: $e');
    }
  }

  // Create a new conversation
  Future<Conversation?> createConversation({String? title}) async {
    try {
      final conversation = await _chatService.createConversation(
        title: title ?? 'New Chat',
      );
      _conversations.insert(0, conversation);
      _currentConversation = conversation;
      notifyListeners();
      return conversation;
    } catch (e) {
      _setError('Failed to create conversation: $e');
      return null;
    }
  }

  // Select a conversation
  void selectConversation(String conversationId) {
    _currentConversation = _conversations.firstWhere(
      (conv) => conv.id == conversationId,
      orElse: () => _conversations.first,
    );
    _clearError();
    notifyListeners();
  }

  // Send a text message
  Future<void> sendMessage(String content) async {
    if (_currentConversation == null) {
      await createConversation();
    }

    if (_currentConversation == null) return;

    // Create user message
    final userMessage = Message(
      content: content,
      type: MessageType.text,
      role: MessageRole.user,
    );

    // Add user message to conversation
    _updateConversation(_currentConversation!.addMessage(userMessage));

    // Show typing indicator
    _setTyping(true);

    try {
      // Send message to service and get response
      final response = await _chatService.sendMessage(
        message: content,
        conversationId: _currentConversation!.id,
      );

      // Create assistant message
      final assistantMessage = Message(
        content: response.message,
        type: MessageType.text,
        role: MessageRole.assistant,
      );

      // Add assistant message to conversation
      _updateConversation(_currentConversation!.addMessage(assistantMessage));
    } catch (e) {
      _setError('Failed to send message: $e');
      
      // Mark user message as failed
      final failedMessage = userMessage.copyWith(status: MessageStatus.failed);
      _updateConversation(_currentConversation!.updateMessage(userMessage.id, failedMessage));
    } finally {
      _setTyping(false);
    }
  }

  // Send a streaming message
  Future<void> sendStreamingMessage(String content) async {
    if (_currentConversation == null) {
      await createConversation();
    }

    if (_currentConversation == null) return;

    // Create user message
    final userMessage = Message(
      content: content,
      type: MessageType.text,
      role: MessageRole.user,
    );

    // Add user message to conversation
    _updateConversation(_currentConversation!.addMessage(userMessage));

    // Create streaming assistant message
    _streamingMessage = Message(
      content: '',
      type: MessageType.text,
      role: MessageRole.assistant,
      status: MessageStatus.typing,
    );

    _updateConversation(_currentConversation!.addMessage(_streamingMessage!));

    try {
      // Start streaming response
      await for (final chunk in _chatService.sendStreamingMessage(
        message: content,
        conversationId: _currentConversation!.id,
      )) {
        _streamingMessage = _streamingMessage!.copyWith(
          content: _streamingMessage!.content + chunk,
        );
        
        _updateConversation(_currentConversation!.updateMessage(
          _streamingMessage!.id,
          _streamingMessage!,
        ));
      }

      // Mark streaming as complete
      _streamingMessage = _streamingMessage!.copyWith(
        status: MessageStatus.sent,
      );
      
      _updateConversation(_currentConversation!.updateMessage(
        _streamingMessage!.id,
        _streamingMessage!,
      ));
    } catch (e) {
      _setError('Failed to send streaming message: $e');
    } finally {
      _streamingMessage = null;
    }
  }

  // Delete a conversation
  Future<void> deleteConversation(String conversationId) async {
    try {
      await _chatService.deleteConversation(conversationId);
      _conversations.removeWhere((conv) => conv.id == conversationId);
      
      if (_currentConversation?.id == conversationId) {
        _currentConversation = _conversations.isNotEmpty ? _conversations.first : null;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete conversation: $e');
    }
  }

  // Update conversation title
  Future<void> updateConversationTitle(String conversationId, String newTitle) async {
    try {
      final conversation = _conversations.firstWhere((conv) => conv.id == conversationId);
      final updatedConversation = conversation.copyWith(title: newTitle);
      
      await _chatService.updateConversation(updatedConversation);
      
      final index = _conversations.indexWhere((conv) => conv.id == conversationId);
      _conversations[index] = updatedConversation;
      
      if (_currentConversation?.id == conversationId) {
        _currentConversation = updatedConversation;
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to update conversation title: $e');
    }
  }

  // Private helper methods
  void _updateConversation(Conversation updatedConversation) {
    final index = _conversations.indexWhere((conv) => conv.id == updatedConversation.id);
    if (index != -1) {
      _conversations[index] = updatedConversation;
    }
    _currentConversation = updatedConversation;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setTyping(bool typing) {
    _isTyping = typing;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
