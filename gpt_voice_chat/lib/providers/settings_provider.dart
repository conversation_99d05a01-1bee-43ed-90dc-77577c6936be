import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _userSettingsKey = 'user_settings';
  
  UserSettings _settings = const UserSettings(userId: 'default_user');
  bool _isLoading = false;
  String? _error;

  // Getters
  UserSettings get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize settings
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await loadSettings();
    } catch (e) {
      _setError('Failed to initialize settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load settings from storage
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_userSettingsKey);
      
      if (settingsJson != null) {
        final Map<String, dynamic> settingsMap = 
            Map<String, dynamic>.from(Uri.splitQueryString(settingsJson));
        _settings = UserSettings.fromJson(settingsMap);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load settings: $e');
    }
  }

  // Save settings to storage
  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = _settings.toJson().toString();
      await prefs.setString(_userSettingsKey, settingsJson);
      _clearError();
    } catch (e) {
      _setError('Failed to save settings: $e');
    }
  }

  // Update language
  Future<void> updateLanguage(Language language) async {
    _settings = _settings.copyWith(language: language);
    await saveSettings();
    notifyListeners();
  }

  // Update theme
  Future<void> updateTheme(Theme theme) async {
    _settings = _settings.copyWith(theme: theme);
    await saveSettings();
    notifyListeners();
  }

  // Update voice settings
  Future<void> updateVoiceSettings(VoiceSettings voiceSettings) async {
    _settings = _settings.copyWith(voiceSettings: voiceSettings);
    await saveSettings();
    notifyListeners();
  }

  // Update voice gender
  Future<void> updateVoiceGender(VoiceGender gender) async {
    final updatedVoiceSettings = _settings.voiceSettings.copyWith(gender: gender);
    await updateVoiceSettings(updatedVoiceSettings);
  }

  // Update voice speed
  Future<void> updateVoiceSpeed(double speed) async {
    final updatedVoiceSettings = _settings.voiceSettings.copyWith(speed: speed);
    await updateVoiceSettings(updatedVoiceSettings);
  }

  // Update voice pitch
  Future<void> updateVoicePitch(double pitch) async {
    final updatedVoiceSettings = _settings.voiceSettings.copyWith(pitch: pitch);
    await updateVoiceSettings(updatedVoiceSettings);
  }

  // Update voice auto-play
  Future<void> updateVoiceAutoPlay(bool autoPlay) async {
    final updatedVoiceSettings = _settings.voiceSettings.copyWith(autoPlay: autoPlay);
    await updateVoiceSettings(updatedVoiceSettings);
  }

  // Update notifications
  Future<void> updateNotifications(bool enabled) async {
    _settings = _settings.copyWith(enableNotifications: enabled);
    await saveSettings();
    notifyListeners();
  }

  // Update sound effects
  Future<void> updateSoundEffects(bool enabled) async {
    _settings = _settings.copyWith(enableSoundEffects: enabled);
    await saveSettings();
    notifyListeners();
  }

  // Update haptic feedback
  Future<void> updateHapticFeedback(bool enabled) async {
    _settings = _settings.copyWith(enableHapticFeedback: enabled);
    await saveSettings();
    notifyListeners();
  }

  // Update auto-save conversations
  Future<void> updateAutoSaveConversations(bool enabled) async {
    _settings = _settings.copyWith(autoSaveConversations: enabled);
    await saveSettings();
    notifyListeners();
  }

  // Update max conversation history
  Future<void> updateMaxConversationHistory(int maxHistory) async {
    _settings = _settings.copyWith(maxConversationHistory: maxHistory);
    await saveSettings();
    notifyListeners();
  }

  // Update voice activation
  Future<void> updateVoiceActivation(bool enabled) async {
    _settings = _settings.copyWith(enableVoiceActivation: enabled);
    await saveSettings();
    notifyListeners();
  }

  // Update voice activation threshold
  Future<void> updateVoiceActivationThreshold(double threshold) async {
    _settings = _settings.copyWith(voiceActivationThreshold: threshold);
    await saveSettings();
    notifyListeners();
  }

  // Reset settings to default
  Future<void> resetToDefaults() async {
    _settings = UserSettings(userId: _settings.userId);
    await saveSettings();
    notifyListeners();
  }

  // Export settings
  Map<String, dynamic> exportSettings() {
    return _settings.toJson();
  }

  // Import settings
  Future<void> importSettings(Map<String, dynamic> settingsJson) async {
    try {
      _settings = UserSettings.fromJson(settingsJson);
      await saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('Failed to import settings: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
