import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import '../models/models.dart';
import '../services/voice_service.dart';

enum VoiceState {
  idle,
  listening,
  processing,
  speaking,
  error,
}

class VoiceProvider extends ChangeNotifier {
  final VoiceService _voiceService = VoiceService();
  final SpeechToText _speechToText = SpeechToText();

  VoiceState _state = VoiceState.idle;
  bool _isInitialized = false;
  String _recognizedText = '';
  String? _error;
  double _soundLevel = 0.0;
  bool _isListening = false;
  bool _isSpeaking = false;

  // Getters
  VoiceState get state => _state;
  bool get isInitialized => _isInitialized;
  String get recognizedText => _recognizedText;
  String? get error => _error;
  double get soundLevel => _soundLevel;
  bool get isListening => _isListening;
  bool get isSpeaking => _isSpeaking;

  // Initialize voice services
  Future<void> initialize() async {
    try {
      _isInitialized = await _speechToText.initialize(
        onError: (error) => _setError('Speech recognition error: ${error.errorMsg}'),
        onStatus: (status) => _handleSpeechStatus(status),
      );
      
      if (!_isInitialized) {
        _setError('Failed to initialize speech recognition');
      }
      
      await _voiceService.initialize();
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize voice services: $e');
    }
  }

  // Start listening for voice input
  Future<void> startListening() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_isInitialized) return;

    try {
      _setState(VoiceState.listening);
      _recognizedText = '';
      _isListening = true;
      
      await _speechToText.listen(
        onResult: (result) {
          _recognizedText = result.recognizedWords;
          _soundLevel = result.hasConfidenceRating ? result.confidence : 0.0;
          notifyListeners();
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        cancelOnError: true,
        listenMode: ListenMode.confirmation,
      );
    } catch (e) {
      _setError('Failed to start listening: $e');
      _setState(VoiceState.error);
    }
  }

  // Stop listening
  Future<void> stopListening() async {
    try {
      await _speechToText.stop();
      _isListening = false;
      _setState(VoiceState.idle);
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop listening: $e');
    }
  }

  // Cancel listening
  Future<void> cancelListening() async {
    try {
      await _speechToText.cancel();
      _isListening = false;
      _recognizedText = '';
      _setState(VoiceState.idle);
      notifyListeners();
    } catch (e) {
      _setError('Failed to cancel listening: $e');
    }
  }

  // Process voice input and get response
  Future<String?> processVoiceInput(String conversationId) async {
    if (_recognizedText.isEmpty) return null;

    try {
      _setState(VoiceState.processing);
      
      final response = await _voiceService.processVoiceMessage(
        text: _recognizedText,
        conversationId: conversationId,
      );

      return response.response;
    } catch (e) {
      _setError('Failed to process voice input: $e');
      _setState(VoiceState.error);
      return null;
    }
  }

  // Speak text
  Future<void> speak(String text) async {
    try {
      _setState(VoiceState.speaking);
      _isSpeaking = true;
      
      await _voiceService.speak(text);
      
      _isSpeaking = false;
      _setState(VoiceState.idle);
    } catch (e) {
      _setError('Failed to speak: $e');
      _setState(VoiceState.error);
      _isSpeaking = false;
    }
  }

  // Stop speaking
  Future<void> stopSpeaking() async {
    try {
      await _voiceService.stopSpeaking();
      _isSpeaking = false;
      _setState(VoiceState.idle);
    } catch (e) {
      _setError('Failed to stop speaking: $e');
    }
  }

  // Start voice conversation mode
  Future<void> startVoiceConversation(String conversationId) async {
    try {
      await startListening();
      // Voice conversation logic will be implemented in the service
    } catch (e) {
      _setError('Failed to start voice conversation: $e');
    }
  }

  // End voice conversation mode
  Future<void> endVoiceConversation() async {
    try {
      await stopListening();
      await stopSpeaking();
      _setState(VoiceState.idle);
    } catch (e) {
      _setError('Failed to end voice conversation: $e');
    }
  }

  // Check if speech recognition is available
  Future<bool> checkAvailability() async {
    try {
      return await _speechToText.initialize();
    } catch (e) {
      return false;
    }
  }

  // Get available locales
  Future<List<LocaleName>> getAvailableLocales() async {
    try {
      return await _speechToText.locales();
    } catch (e) {
      return [];
    }
  }

  // Private helper methods
  void _setState(VoiceState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _state = VoiceState.error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _handleSpeechStatus(String status) {
    switch (status) {
      case 'listening':
        _isListening = true;
        _setState(VoiceState.listening);
        break;
      case 'notListening':
        _isListening = false;
        if (_state == VoiceState.listening) {
          _setState(VoiceState.idle);
        }
        break;
      case 'done':
        _isListening = false;
        _setState(VoiceState.idle);
        break;
    }
    notifyListeners();
  }

  @override
  void dispose() {
    _speechToText.cancel();
    _voiceService.dispose();
    super.dispose();
  }
}
