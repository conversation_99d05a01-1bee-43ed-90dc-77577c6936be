// API Response models for backend communication

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;
  final int? statusCode;
  final Map<String, dynamic>? metadata;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
    this.statusCode,
    this.metadata,
  });

  factory ApiResponse.success({
    T? data,
    String? message,
    int? statusCode,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode ?? 200,
      metadata: metadata,
    );
  }

  factory ApiResponse.error({
    String? error,
    String? message,
    int? statusCode,
    Map<String, dynamic>? metadata,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
      statusCode: statusCode ?? 500,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data,
      'message': message,
      'error': error,
      'statusCode': statusCode,
      'metadata': metadata,
    };
  }

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : json['data'],
      message: json['message'],
      error: json['error'],
      statusCode: json['statusCode'],
      metadata: json['metadata'],
    );
  }
}

class ChatRequest {
  final String message;
  final String conversationId;
  final bool stream;
  final Map<String, dynamic>? context;

  const ChatRequest({
    required this.message,
    required this.conversationId,
    this.stream = false,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'conversationId': conversationId,
      'stream': stream,
      'context': context,
    };
  }
}

class ChatResponse {
  final String id;
  final String message;
  final String conversationId;
  final DateTime timestamp;
  final bool isComplete;
  final Map<String, dynamic>? metadata;

  const ChatResponse({
    required this.id,
    required this.message,
    required this.conversationId,
    required this.timestamp,
    this.isComplete = true,
    this.metadata,
  });

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    return ChatResponse(
      id: json['id'],
      message: json['message'],
      conversationId: json['conversationId'],
      timestamp: DateTime.parse(json['timestamp']),
      isComplete: json['isComplete'] ?? true,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'conversationId': conversationId,
      'timestamp': timestamp.toIso8601String(),
      'isComplete': isComplete,
      'metadata': metadata,
    };
  }
}

class VoiceRequest {
  final String audioData;
  final String conversationId;
  final String format;
  final int sampleRate;
  final Map<String, dynamic>? context;

  const VoiceRequest({
    required this.audioData,
    required this.conversationId,
    this.format = 'wav',
    this.sampleRate = 16000,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'audioData': audioData,
      'conversationId': conversationId,
      'format': format,
      'sampleRate': sampleRate,
      'context': context,
    };
  }
}

class VoiceResponse {
  final String id;
  final String transcription;
  final String response;
  final String audioUrl;
  final String conversationId;
  final DateTime timestamp;
  final Duration? audioDuration;
  final Map<String, dynamic>? metadata;

  const VoiceResponse({
    required this.id,
    required this.transcription,
    required this.response,
    required this.audioUrl,
    required this.conversationId,
    required this.timestamp,
    this.audioDuration,
    this.metadata,
  });

  factory VoiceResponse.fromJson(Map<String, dynamic> json) {
    return VoiceResponse(
      id: json['id'],
      transcription: json['transcription'],
      response: json['response'],
      audioUrl: json['audioUrl'],
      conversationId: json['conversationId'],
      timestamp: DateTime.parse(json['timestamp']),
      audioDuration: json['audioDuration'] != null
          ? Duration(milliseconds: json['audioDuration'])
          : null,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transcription': transcription,
      'response': response,
      'audioUrl': audioUrl,
      'conversationId': conversationId,
      'timestamp': timestamp.toIso8601String(),
      'audioDuration': audioDuration?.inMilliseconds,
      'metadata': metadata,
    };
  }
}

class ConversationListResponse {
  final List<Map<String, dynamic>> conversations;
  final int total;
  final int page;
  final int limit;

  const ConversationListResponse({
    required this.conversations,
    required this.total,
    required this.page,
    required this.limit,
  });

  factory ConversationListResponse.fromJson(Map<String, dynamic> json) {
    return ConversationListResponse(
      conversations: List<Map<String, dynamic>>.from(json['conversations']),
      total: json['total'],
      page: json['page'],
      limit: json['limit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'conversations': conversations,
      'total': total,
      'page': page,
      'limit': limit,
    };
  }
}
