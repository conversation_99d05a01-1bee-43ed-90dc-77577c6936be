import 'package:uuid/uuid.dart';
import 'message.dart';

enum ConversationType {
  text,
  voice,
  mixed,
}

class Conversation {
  final String id;
  final String title;
  final List<Message> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ConversationType type;
  final bool isPinned;
  final Map<String, dynamic>? metadata;

  Conversation({
    String? id,
    required this.title,
    List<Message>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.type = ConversationType.mixed,
    this.isPinned = false,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        messages = messages ?? [],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Conversation copyWith({
    String? id,
    String? title,
    List<Message>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    ConversationType? type,
    bool? isPinned,
    Map<String, dynamic>? metadata,
  }) {
    return Conversation(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      isPinned: isPinned ?? this.isPinned,
      metadata: metadata ?? this.metadata,
    );
  }

  // Get the last message in the conversation
  Message? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  // Get a preview of the conversation (first few words of last message)
  String get preview {
    if (messages.isEmpty) return 'No messages yet';
    final lastMsg = messages.last;
    if (lastMsg.content.length > 50) {
      return '${lastMsg.content.substring(0, 50)}...';
    }
    return lastMsg.content;
  }

  // Check if conversation has unread messages
  bool get hasUnreadMessages {
    return messages.any((msg) => 
        msg.role == MessageRole.assistant && 
        msg.status != MessageStatus.delivered);
  }

  // Add a message to the conversation
  Conversation addMessage(Message message) {
    final updatedMessages = List<Message>.from(messages)..add(message);
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  // Update a message in the conversation
  Conversation updateMessage(String messageId, Message updatedMessage) {
    final updatedMessages = messages.map((msg) {
      return msg.id == messageId ? updatedMessage : msg;
    }).toList();
    
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  // Remove a message from the conversation
  Conversation removeMessage(String messageId) {
    final updatedMessages = messages.where((msg) => msg.id != messageId).toList();
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'type': type.name,
      'isPinned': isPinned,
      'metadata': metadata,
    };
  }

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'],
      title: json['title'],
      messages: (json['messages'] as List<dynamic>)
          .map((msgJson) => Message.fromJson(msgJson))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      type: ConversationType.values.firstWhere((e) => e.name == json['type']),
      isPinned: json['isPinned'] ?? false,
      metadata: json['metadata'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Conversation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Conversation(id: $id, title: $title, messagesCount: ${messages.length}, createdAt: $createdAt)';
  }
}
