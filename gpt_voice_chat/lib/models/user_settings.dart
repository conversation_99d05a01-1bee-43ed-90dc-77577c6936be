enum VoiceGender {
  male,
  female,
  neutral,
}

enum Language {
  english,
  spanish,
  french,
  german,
  chinese,
  japanese,
  korean,
}

enum Theme {
  light,
  dark,
  system,
}

class VoiceSettings {
  final VoiceGender gender;
  final double speed;
  final double pitch;
  final String voiceId;
  final bool autoPlay;

  const VoiceSettings({
    this.gender = VoiceGender.neutral,
    this.speed = 1.0,
    this.pitch = 1.0,
    this.voiceId = 'default',
    this.autoPlay = true,
  });

  VoiceSettings copyWith({
    VoiceGender? gender,
    double? speed,
    double? pitch,
    String? voiceId,
    bool? autoPlay,
  }) {
    return VoiceSettings(
      gender: gender ?? this.gender,
      speed: speed ?? this.speed,
      pitch: pitch ?? this.pitch,
      voiceId: voiceId ?? this.voiceId,
      autoPlay: autoPlay ?? this.autoPlay,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gender': gender.name,
      'speed': speed,
      'pitch': pitch,
      'voiceId': voiceId,
      'autoPlay': autoPlay,
    };
  }

  factory VoiceSettings.fromJson(Map<String, dynamic> json) {
    return VoiceSettings(
      gender: VoiceGender.values.firstWhere(
        (e) => e.name == json['gender'],
        orElse: () => VoiceGender.neutral,
      ),
      speed: json['speed']?.toDouble() ?? 1.0,
      pitch: json['pitch']?.toDouble() ?? 1.0,
      voiceId: json['voiceId'] ?? 'default',
      autoPlay: json['autoPlay'] ?? true,
    );
  }
}

class UserSettings {
  final String userId;
  final Language language;
  final Theme theme;
  final VoiceSettings voiceSettings;
  final bool enableNotifications;
  final bool enableSoundEffects;
  final bool enableHapticFeedback;
  final bool autoSaveConversations;
  final int maxConversationHistory;
  final bool enableVoiceActivation;
  final double voiceActivationThreshold;
  final Map<String, dynamic>? customSettings;

  const UserSettings({
    required this.userId,
    this.language = Language.english,
    this.theme = Theme.system,
    this.voiceSettings = const VoiceSettings(),
    this.enableNotifications = true,
    this.enableSoundEffects = true,
    this.enableHapticFeedback = true,
    this.autoSaveConversations = true,
    this.maxConversationHistory = 100,
    this.enableVoiceActivation = false,
    this.voiceActivationThreshold = 0.5,
    this.customSettings,
  });

  UserSettings copyWith({
    String? userId,
    Language? language,
    Theme? theme,
    VoiceSettings? voiceSettings,
    bool? enableNotifications,
    bool? enableSoundEffects,
    bool? enableHapticFeedback,
    bool? autoSaveConversations,
    int? maxConversationHistory,
    bool? enableVoiceActivation,
    double? voiceActivationThreshold,
    Map<String, dynamic>? customSettings,
  }) {
    return UserSettings(
      userId: userId ?? this.userId,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      voiceSettings: voiceSettings ?? this.voiceSettings,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSoundEffects: enableSoundEffects ?? this.enableSoundEffects,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      autoSaveConversations: autoSaveConversations ?? this.autoSaveConversations,
      maxConversationHistory: maxConversationHistory ?? this.maxConversationHistory,
      enableVoiceActivation: enableVoiceActivation ?? this.enableVoiceActivation,
      voiceActivationThreshold: voiceActivationThreshold ?? this.voiceActivationThreshold,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'language': language.name,
      'theme': theme.name,
      'voiceSettings': voiceSettings.toJson(),
      'enableNotifications': enableNotifications,
      'enableSoundEffects': enableSoundEffects,
      'enableHapticFeedback': enableHapticFeedback,
      'autoSaveConversations': autoSaveConversations,
      'maxConversationHistory': maxConversationHistory,
      'enableVoiceActivation': enableVoiceActivation,
      'voiceActivationThreshold': voiceActivationThreshold,
      'customSettings': customSettings,
    };
  }

  factory UserSettings.fromJson(Map<String, dynamic> json) {
    return UserSettings(
      userId: json['userId'],
      language: Language.values.firstWhere(
        (e) => e.name == json['language'],
        orElse: () => Language.english,
      ),
      theme: Theme.values.firstWhere(
        (e) => e.name == json['theme'],
        orElse: () => Theme.system,
      ),
      voiceSettings: VoiceSettings.fromJson(json['voiceSettings'] ?? {}),
      enableNotifications: json['enableNotifications'] ?? true,
      enableSoundEffects: json['enableSoundEffects'] ?? true,
      enableHapticFeedback: json['enableHapticFeedback'] ?? true,
      autoSaveConversations: json['autoSaveConversations'] ?? true,
      maxConversationHistory: json['maxConversationHistory'] ?? 100,
      enableVoiceActivation: json['enableVoiceActivation'] ?? false,
      voiceActivationThreshold: json['voiceActivationThreshold']?.toDouble() ?? 0.5,
      customSettings: json['customSettings'],
    );
  }

  @override
  String toString() {
    return 'UserSettings(userId: $userId, language: $language, theme: $theme)';
  }
}
