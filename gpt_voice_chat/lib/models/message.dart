import 'package:uuid/uuid.dart';

enum MessageType {
  text,
  voice,
  image,
  system,
}

enum MessageRole {
  user,
  assistant,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  failed,
  typing,
}

class Message {
  final String id;
  final String content;
  final MessageType type;
  final MessageRole role;
  final DateTime timestamp;
  final MessageStatus status;
  final String? audioUrl;
  final Duration? audioDuration;
  final Map<String, dynamic>? metadata;

  Message({
    String? id,
    required this.content,
    required this.type,
    required this.role,
    DateTime? timestamp,
    this.status = MessageStatus.sent,
    this.audioUrl,
    this.audioDuration,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now();

  Message copyWith({
    String? id,
    String? content,
    MessageType? type,
    MessageRole? role,
    DateTime? timestamp,
    MessageStatus? status,
    String? audioUrl,
    Duration? audioDuration,
    Map<String, dynamic>? metadata,
  }) {
    return Message(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      role: role ?? this.role,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      audioUrl: audioUrl ?? this.audioUrl,
      audioDuration: audioDuration ?? this.audioDuration,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'role': role.name,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'audioUrl': audioUrl,
      'audioDuration': audioDuration?.inMilliseconds,
      'metadata': metadata,
    };
  }

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      content: json['content'],
      type: MessageType.values.firstWhere((e) => e.name == json['type']),
      role: MessageRole.values.firstWhere((e) => e.name == json['role']),
      timestamp: DateTime.parse(json['timestamp']),
      status: MessageStatus.values.firstWhere((e) => e.name == json['status']),
      audioUrl: json['audioUrl'],
      audioDuration: json['audioDuration'] != null
          ? Duration(milliseconds: json['audioDuration'])
          : null,
      metadata: json['metadata'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Message(id: $id, content: $content, type: $type, role: $role, timestamp: $timestamp, status: $status)';
  }
}
