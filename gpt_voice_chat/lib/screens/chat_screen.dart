import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/chat_provider.dart';
import '../providers/voice_provider.dart';
import '../widgets/widgets.dart';
import '../widgets/voice_input_widget.dart';
import '../constants/app_constants.dart';
import 'voice_call_screen.dart';

class ChatScreen extends StatefulWidget {
  final String conversationId;

  const ChatScreen({
    super.key,
    required this.conversationId,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showVoiceInput = false;

  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      chatProvider.selectConversation(widget.conversationId);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          final conversation = chatProvider.currentConversation;
          
          if (conversation == null) {
            return _buildLoadingState();
          }

          return Column(
            children: [
              Expanded(
                child: MessageList(
                  messages: conversation.messages,
                  isLoading: chatProvider.isLoading,
                  scrollController: _scrollController,
                  onMessageTap: _handleMessageTap,
                  onMessageLongPress: _handleMessageLongPress,
                ),
              ),
              if (chatProvider.isTyping)
                SimpleTypingIndicator(isVisible: true),
              _buildInputArea(chatProvider),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          final conversation = chatProvider.currentConversation;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                conversation?.title ?? 'Chat',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              if (chatProvider.isTyping)
                Text(
                  'AI is typing...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
            ],
          );
        },
      ),
      actions: [
        IconButton(
          onPressed: _startVoiceCall,
          icon: Icon(Icons.call),
          tooltip: 'Start Voice Call',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'clear',
              child: Row(
                children: [
                  Icon(Icons.clear_all, size: 18),
                  SizedBox(width: 12),
                  Text('Clear Chat'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 18),
                  SizedBox(width: 12),
                  Text('Export Chat'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading conversation...'),
        ],
      ),
    );
  }

  Widget _buildInputArea(ChatProvider chatProvider) {
    if (_showVoiceInput) {
      return VoiceInputWidget(
        conversationId: widget.conversationId,
        onTranscriptionComplete: (text) {
          setState(() {
            _showVoiceInput = false;
          });
          _sendMessage(text, chatProvider);
        },
        onCancel: () {
          setState(() {
            _showVoiceInput = false;
          });
        },
      );
    }

    return Consumer<VoiceProvider>(
      builder: (context, voiceProvider, child) {
        return ChatInput(
          onSendMessage: (message) => _sendMessage(message, chatProvider),
          onVoicePressed: _handleVoiceInput,
          isLoading: chatProvider.isLoading,
          isListening: voiceProvider.isListening,
          placeholder: 'Type a message or use voice input...',
        );
      },
    );
  }

  void _sendMessage(String message, ChatProvider chatProvider) {
    if (message.trim().isEmpty) return;
    
    // Use streaming for better user experience
    chatProvider.sendStreamingMessage(message);
  }

  void _handleVoiceInput() {
    setState(() {
      _showVoiceInput = true;
    });
  }

  void _startVoiceCall() {
    final conversation = Provider.of<ChatProvider>(context, listen: false).currentConversation;
    
    if (conversation != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => VoiceCallScreen(
            conversationId: conversation.id,
            conversationTitle: conversation.title,
          ),
        ),
      );
    }
  }

  void _handleMessageTap(Message message) {
    // Handle message tap (e.g., play audio for voice messages)
    if (message.type == MessageType.voice && message.audioUrl != null) {
      // Play audio
      _playAudioMessage(message);
    }
  }

  void _handleMessageLongPress(Message message) {
    // Show message options
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildMessageOptionsSheet(message),
    );
  }

  Widget _buildMessageOptionsSheet(Message message) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(height: 16),
          ListTile(
            leading: Icon(Icons.copy),
            title: Text('Copy'),
            onTap: () {
              // Copy message to clipboard
              Navigator.of(context).pop();
            },
          ),
          if (message.type == MessageType.voice)
            ListTile(
              leading: Icon(Icons.play_arrow),
              title: Text('Play Audio'),
              onTap: () {
                Navigator.of(context).pop();
                _playAudioMessage(message);
              },
            ),
          ListTile(
            leading: Icon(Icons.share),
            title: Text('Share'),
            onTap: () {
              // Share message
              Navigator.of(context).pop();
            },
          ),
          if (message.role == MessageRole.user)
            ListTile(
              leading: Icon(Icons.delete, color: theme.colorScheme.error),
              title: Text(
                'Delete',
                style: TextStyle(color: theme.colorScheme.error),
              ),
              onTap: () {
                Navigator.of(context).pop();
                _deleteMessage(message);
              },
            ),
        ],
      ),
    );
  }

  void _playAudioMessage(Message message) {
    // Implement audio playback
    final voiceProvider = Provider.of<VoiceProvider>(context, listen: false);
    if (message.content.isNotEmpty) {
      voiceProvider.speak(message.content);
    }
  }

  void _deleteMessage(Message message) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Message'),
        content: Text('Are you sure you want to delete this message?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Delete message logic would go here
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear':
        _clearChat();
        break;
      case 'export':
        _exportChat();
        break;
    }
  }

  void _clearChat() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear Chat'),
        content: Text('Are you sure you want to clear all messages in this conversation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Clear chat logic would go here
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _exportChat() {
    // Implement chat export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Export functionality coming soon!'),
      ),
    );
  }
}
