import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Settings'),
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          return ListView(
            children: [
              _buildSection(
                context,
                'Voice Settings',
                Icons.mic,
                [
                  _buildVoiceGenderSetting(context, settingsProvider),
                  _buildVoiceSpeedSetting(context, settingsProvider),
                  _buildVoicePitchSetting(context, settingsProvider),
                  _buildVoiceAutoPlaySetting(context, settingsProvider),
                ],
              ),
              _buildSection(
                context,
                'General',
                Icons.settings,
                [
                  _buildLanguageSetting(context, settingsProvider),
                  _buildThemeSetting(context, settingsProvider),
                  _buildNotificationsSetting(context, settingsProvider),
                  _buildSoundEffectsSetting(context, settingsProvider),
                  _buildHapticFeedbackSetting(context, settingsProvider),
                ],
              ),
              _buildSection(
                context,
                'Chat Settings',
                Icons.chat,
                [
                  _buildAutoSaveSetting(context, settingsProvider),
                  _buildMaxHistorySetting(context, settingsProvider),
                  _buildVoiceActivationSetting(context, settingsProvider),
                ],
              ),
              _buildSection(
                context,
                'About',
                Icons.info,
                [
                  _buildAboutTile(context),
                  _buildVersionTile(context),
                  _buildResetSettingsTile(context, settingsProvider),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, IconData icon, List<Widget> children) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Row(
            children: [
              Icon(icon, size: 20, color: theme.colorScheme.primary),
              SizedBox(width: 8),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        ...children,
        SizedBox(height: 8),
      ],
    );
  }

  Widget _buildVoiceGenderSetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Voice Gender'),
      subtitle: Text(_getVoiceGenderLabel(provider.settings.voiceSettings.gender)),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showVoiceGenderDialog(context, provider),
    );
  }

  Widget _buildVoiceSpeedSetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Voice Speed'),
      subtitle: Text('${(provider.settings.voiceSettings.speed * 100).round()}%'),
      trailing: SizedBox(
        width: 150,
        child: Slider(
          value: provider.settings.voiceSettings.speed,
          min: AppConstants.minVoiceSpeed,
          max: AppConstants.maxVoiceSpeed,
          divisions: 15,
          onChanged: (value) => provider.updateVoiceSpeed(value),
        ),
      ),
    );
  }

  Widget _buildVoicePitchSetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Voice Pitch'),
      subtitle: Text('${(provider.settings.voiceSettings.pitch * 100).round()}%'),
      trailing: SizedBox(
        width: 150,
        child: Slider(
          value: provider.settings.voiceSettings.pitch,
          min: AppConstants.minVoicePitch,
          max: AppConstants.maxVoicePitch,
          divisions: 15,
          onChanged: (value) => provider.updateVoicePitch(value),
        ),
      ),
    );
  }

  Widget _buildVoiceAutoPlaySetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Auto-play Voice Responses'),
      subtitle: Text('Automatically play AI voice responses'),
      value: provider.settings.voiceSettings.autoPlay,
      onChanged: (value) => provider.updateVoiceAutoPlay(value),
    );
  }

  Widget _buildLanguageSetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Language'),
      subtitle: Text(_getLanguageLabel(provider.settings.language)),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showLanguageDialog(context, provider),
    );
  }

  Widget _buildThemeSetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Theme'),
      subtitle: Text(_getThemeLabel(provider.settings.theme)),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showThemeDialog(context, provider),
    );
  }

  Widget _buildNotificationsSetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Notifications'),
      subtitle: Text('Receive push notifications'),
      value: provider.settings.enableNotifications,
      onChanged: (value) => provider.updateNotifications(value),
    );
  }

  Widget _buildSoundEffectsSetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Sound Effects'),
      subtitle: Text('Play sound effects for interactions'),
      value: provider.settings.enableSoundEffects,
      onChanged: (value) => provider.updateSoundEffects(value),
    );
  }

  Widget _buildHapticFeedbackSetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Haptic Feedback'),
      subtitle: Text('Vibrate for touch interactions'),
      value: provider.settings.enableHapticFeedback,
      onChanged: (value) => provider.updateHapticFeedback(value),
    );
  }

  Widget _buildAutoSaveSetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Auto-save Conversations'),
      subtitle: Text('Automatically save chat history'),
      value: provider.settings.autoSaveConversations,
      onChanged: (value) => provider.updateAutoSaveConversations(value),
    );
  }

  Widget _buildMaxHistorySetting(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Max Conversation History'),
      subtitle: Text('${provider.settings.maxConversationHistory} conversations'),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showMaxHistoryDialog(context, provider),
    );
  }

  Widget _buildVoiceActivationSetting(BuildContext context, SettingsProvider provider) {
    return SwitchListTile(
      title: Text('Voice Activation'),
      subtitle: Text('Start listening with voice commands'),
      value: provider.settings.enableVoiceActivation,
      onChanged: (value) => provider.updateVoiceActivation(value),
    );
  }

  Widget _buildAboutTile(BuildContext context) {
    return ListTile(
      title: Text('About ${AppConstants.appName}'),
      subtitle: Text('Learn more about this app'),
      trailing: Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _showAboutDialog(context),
    );
  }

  Widget _buildVersionTile(BuildContext context) {
    return ListTile(
      title: Text('Version'),
      subtitle: Text(AppConstants.appVersion),
    );
  }

  Widget _buildResetSettingsTile(BuildContext context, SettingsProvider provider) {
    return ListTile(
      title: Text('Reset Settings'),
      subtitle: Text('Reset all settings to default'),
      trailing: Icon(Icons.refresh),
      onTap: () => _showResetDialog(context, provider),
    );
  }

  // Dialog methods
  void _showVoiceGenderDialog(BuildContext context, SettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Voice Gender'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: VoiceGender.values.map((gender) {
            return RadioListTile<VoiceGender>(
              title: Text(_getVoiceGenderLabel(gender)),
              value: gender,
              groupValue: provider.settings.voiceSettings.gender,
              onChanged: (value) {
                if (value != null) {
                  provider.updateVoiceGender(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, SettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Language'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: Language.values.map((language) {
            return RadioListTile<Language>(
              title: Text(_getLanguageLabel(language)),
              value: language,
              groupValue: provider.settings.language,
              onChanged: (value) {
                if (value != null) {
                  provider.updateLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context, SettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: Theme.values.map((theme) {
            return RadioListTile<Theme>(
              title: Text(_getThemeLabel(theme)),
              value: theme,
              groupValue: provider.settings.theme,
              onChanged: (value) {
                if (value != null) {
                  provider.updateTheme(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showMaxHistoryDialog(BuildContext context, SettingsProvider provider) {
    final controller = TextEditingController(
      text: provider.settings.maxConversationHistory.toString(),
    );
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Max Conversation History'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Number of conversations',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final value = int.tryParse(controller.text);
              if (value != null && value > 0) {
                provider.updateMaxConversationHistory(value);
                Navigator.of(context).pop();
              }
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Icon(Icons.chat, size: 48),
      children: [
        Text('A ChatGPT-like mobile app with voice capabilities.'),
        SizedBox(height: 16),
        Text('Built with Flutter and powered by AI.'),
      ],
    );
  }

  void _showResetDialog(BuildContext context, SettingsProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Settings'),
        content: Text('Are you sure you want to reset all settings to their default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.resetToDefaults();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Reset'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getVoiceGenderLabel(VoiceGender gender) {
    switch (gender) {
      case VoiceGender.male:
        return 'Male';
      case VoiceGender.female:
        return 'Female';
      case VoiceGender.neutral:
        return 'Neutral';
    }
  }

  String _getLanguageLabel(Language language) {
    switch (language) {
      case Language.english:
        return 'English';
      case Language.spanish:
        return 'Spanish';
      case Language.french:
        return 'French';
      case Language.german:
        return 'German';
      case Language.chinese:
        return 'Chinese';
      case Language.japanese:
        return 'Japanese';
      case Language.korean:
        return 'Korean';
    }
  }

  String _getThemeLabel(Theme theme) {
    switch (theme) {
      case Theme.light:
        return 'Light';
      case Theme.dark:
        return 'Dark';
      case Theme.system:
        return 'System';
    }
  }
}
