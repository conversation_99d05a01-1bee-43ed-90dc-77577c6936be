import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

class PermissionHelper {
  // Check if microphone permission is granted
  static Future<bool> hasMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  // Request microphone permission
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  // Check if microphone permission is permanently denied
  static Future<bool> isMicrophonePermissionPermanentlyDenied() async {
    final status = await Permission.microphone.status;
    return status.isPermanentlyDenied;
  }

  // Show permission dialog
  static Future<bool> showPermissionDialog(BuildContext context) async {
    final hasPermission = await hasMicrophonePermission();
    
    if (hasPermission) {
      return true;
    }

    final isPermanentlyDenied = await isMicrophonePermissionPermanentlyDenied();
    
    if (isPermanentlyDenied) {
      return await _showSettingsDialog(context);
    }

    return await _showRequestDialog(context);
  }

  // Show dialog to request permission
  static Future<bool> _showRequestDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.mic,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 12),
              Text('Microphone Permission'),
            ],
          ),
          content: Text(
            'This app needs access to your microphone to enable voice input features. '
            'Your voice data is processed securely and not stored permanently.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Allow'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      return await requestMicrophonePermission();
    }

    return false;
  }

  // Show dialog to open settings
  static Future<bool> _showSettingsDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.settings,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 12),
              Text('Permission Required'),
            ],
          ),
          content: Text(
            'Microphone permission is required for voice input. '
            'Please enable it in the app settings to use voice features.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Open Settings'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      await openAppSettings();
      // Check permission again after returning from settings
      return await hasMicrophonePermission();
    }

    return false;
  }

  // Show permission denied snackbar
  static void showPermissionDeniedSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.warning,
              color: Colors.white,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Microphone permission is required for voice input',
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: SnackBarAction(
          label: 'Settings',
          textColor: Colors.white,
          onPressed: () => openAppSettings(),
        ),
      ),
    );
  }

  // Check and request permission with UI feedback
  static Future<bool> checkAndRequestPermission(BuildContext context) async {
    final hasPermission = await hasMicrophonePermission();
    
    if (hasPermission) {
      return true;
    }

    final granted = await showPermissionDialog(context);
    
    if (!granted) {
      showPermissionDeniedSnackbar(context);
    }

    return granted;
  }
}

// Widget to handle permission state
class PermissionWrapper extends StatefulWidget {
  final Widget child;
  final Widget? permissionDeniedWidget;
  final Function()? onPermissionGranted;
  final Function()? onPermissionDenied;

  const PermissionWrapper({
    super.key,
    required this.child,
    this.permissionDeniedWidget,
    this.onPermissionGranted,
    this.onPermissionDenied,
  });

  @override
  State<PermissionWrapper> createState() => _PermissionWrapperState();
}

class _PermissionWrapperState extends State<PermissionWrapper> {
  bool _hasPermission = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkPermission();
  }

  Future<void> _checkPermission() async {
    final hasPermission = await PermissionHelper.hasMicrophonePermission();
    
    setState(() {
      _hasPermission = hasPermission;
      _isLoading = false;
    });

    if (hasPermission) {
      widget.onPermissionGranted?.call();
    } else {
      widget.onPermissionDenied?.call();
    }
  }

  Future<void> _requestPermission() async {
    final granted = await PermissionHelper.checkAndRequestPermission(context);
    
    setState(() {
      _hasPermission = granted;
    });

    if (granted) {
      widget.onPermissionGranted?.call();
    } else {
      widget.onPermissionDenied?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasPermission) {
      return widget.child;
    }

    if (widget.permissionDeniedWidget != null) {
      return widget.permissionDeniedWidget!;
    }

    return _buildDefaultPermissionWidget();
  }

  Widget _buildDefaultPermissionWidget() {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic_off,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            SizedBox(height: 24),
            Text(
              'Microphone Permission Required',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'To use voice input features, please grant microphone permission.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _requestPermission,
              icon: Icon(Icons.mic),
              label: Text('Grant Permission'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
