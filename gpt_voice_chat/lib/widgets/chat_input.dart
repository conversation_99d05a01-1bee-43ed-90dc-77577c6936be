import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ChatInput extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback? onVoicePressed;
  final VoidCallback? onAttachPressed;
  final bool isLoading;
  final bool isListening;
  final String? placeholder;

  const ChatInput({
    super.key,
    required this.onSendMessage,
    this.onVoicePressed,
    this.onAttachPressed,
    this.isLoading = false,
    this.isListening = false,
    this.placeholder,
  });

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> with TickerProviderStateMixin {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _hasText = false;
  late AnimationController _voiceAnimationController;
  late Animation<double> _voiceAnimation;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onTextChanged);
    
    _voiceAnimationController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _voiceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _voiceAnimationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isListening) {
      _voiceAnimationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(ChatInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isListening != oldWidget.isListening) {
      if (widget.isListening) {
        _voiceAnimationController.repeat(reverse: true);
      } else {
        _voiceAnimationController.stop();
        _voiceAnimationController.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _voiceAnimationController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty && !widget.isLoading) {
      widget.onSendMessage(text);
      _controller.clear();
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            if (widget.onAttachPressed != null) _buildAttachButton(theme),
            Expanded(child: _buildTextField(theme)),
            SizedBox(width: 8),
            _buildActionButton(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachButton(ThemeData theme) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: IconButton(
        onPressed: widget.onAttachPressed,
        icon: Icon(
          Icons.attach_file,
          color: theme.colorScheme.onSurface.withOpacity(0.6),
        ),
        style: IconButton.styleFrom(
          backgroundColor: theme.colorScheme.surfaceVariant.withOpacity(0.5),
          shape: CircleBorder(),
        ),
      ),
    );
  }

  Widget _buildTextField(ThemeData theme) {
    return Container(
      constraints: BoxConstraints(maxHeight: 120),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: _focusNode.hasFocus 
              ? theme.colorScheme.primary.withOpacity(0.5)
              : Colors.transparent,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        maxLines: null,
        textInputAction: TextInputAction.newline,
        keyboardType: TextInputType.multiline,
        enabled: !widget.isLoading,
        decoration: InputDecoration(
          hintText: widget.placeholder ?? 'Type a message...',
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        ),
        style: theme.textTheme.bodyMedium,
        onSubmitted: (_) => _sendMessage(),
      ),
    );
  }

  Widget _buildActionButton(ThemeData theme) {
    if (widget.isLoading) {
      return _buildLoadingButton(theme);
    }

    if (_hasText) {
      return _buildSendButton(theme);
    }

    return _buildVoiceButton(theme);
  }

  Widget _buildSendButton(ThemeData theme) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      child: IconButton(
        onPressed: _sendMessage,
        icon: Icon(
          Icons.send,
          color: Colors.white,
        ),
        style: IconButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          shape: CircleBorder(),
          padding: EdgeInsets.all(12),
        ),
      ),
    );
  }

  Widget _buildVoiceButton(ThemeData theme) {
    return AnimatedBuilder(
      animation: _voiceAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isListening ? _voiceAnimation.value : 1.0,
          child: IconButton(
            onPressed: widget.onVoicePressed,
            icon: Icon(
              widget.isListening ? Icons.mic : Icons.mic_none,
              color: widget.isListening 
                  ? Colors.white 
                  : theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            style: IconButton.styleFrom(
              backgroundColor: widget.isListening 
                  ? theme.colorScheme.error
                  : theme.colorScheme.surfaceVariant.withOpacity(0.5),
              shape: CircleBorder(),
              padding: EdgeInsets.all(12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingButton(ThemeData theme) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),
        ),
      ),
    );
  }
}
