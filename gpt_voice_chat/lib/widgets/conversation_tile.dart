import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';

class ConversationTile extends StatelessWidget {
  final Conversation conversation;
  final VoidCallback onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;
  final VoidCallback? onPin;

  const ConversationTile({
    super.key,
    required this.conversation,
    required this.onTap,
    this.onDelete,
    this.onEdit,
    this.onPin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              _buildAvatar(theme),
              Sized<PERSON>ox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(theme),
                    <PERSON><PERSON><PERSON><PERSON>(height: 4),
                    _buildPreview(theme),
                    SizedBox(height: 8),
                    _buildMetadata(theme),
                  ],
                ),
              ),
              _buildTrailing(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getAvatarColor(theme),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getConversationIcon(),
        color: Colors.white,
        size: 24,
      ),
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        if (conversation.isPinned)
          Padding(
            padding: EdgeInsets.only(right: 8),
            child: Icon(
              Icons.push_pin,
              size: 16,
              color: theme.colorScheme.primary,
            ),
          ),
        Expanded(
          child: Text(
            conversation.title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          _formatTime(conversation.updatedAt),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildPreview(ThemeData theme) {
    return Text(
      conversation.preview,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: theme.colorScheme.onSurface.withOpacity(0.7),
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMetadata(ThemeData theme) {
    return Row(
      children: [
        _buildTypeChip(theme),
        SizedBox(width: 8),
        Text(
          '${conversation.messages.length} messages',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
        if (conversation.hasUnreadMessages) ...[
          SizedBox(width: 8),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTypeChip(ThemeData theme) {
    String label;
    IconData icon;
    Color color;

    switch (conversation.type) {
      case ConversationType.voice:
        label = 'Voice';
        icon = Icons.mic;
        color = theme.colorScheme.secondary;
        break;
      case ConversationType.text:
        label = 'Text';
        icon = Icons.chat;
        color = theme.colorScheme.primary;
        break;
      case ConversationType.mixed:
        label = 'Mixed';
        icon = Icons.chat_bubble;
        color = theme.colorScheme.tertiary;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrailing(ThemeData theme) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'pin':
            onPin?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 18),
              SizedBox(width: 12),
              Text('Edit Title'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'pin',
          child: Row(
            children: [
              Icon(
                conversation.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                size: 18,
              ),
              SizedBox(width: 12),
              Text(conversation.isPinned ? 'Unpin' : 'Pin'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 18, color: theme.colorScheme.error),
              SizedBox(width: 12),
              Text(
                'Delete',
                style: TextStyle(color: theme.colorScheme.error),
              ),
            ],
          ),
        ),
      ],
      child: Icon(
        Icons.more_vert,
        color: theme.colorScheme.onSurface.withOpacity(0.6),
      ),
    );
  }

  Color _getAvatarColor(ThemeData theme) {
    switch (conversation.type) {
      case ConversationType.voice:
        return theme.colorScheme.secondary;
      case ConversationType.text:
        return theme.colorScheme.primary;
      case ConversationType.mixed:
        return theme.colorScheme.tertiary;
    }
  }

  IconData _getConversationIcon() {
    switch (conversation.type) {
      case ConversationType.voice:
        return Icons.mic;
      case ConversationType.text:
        return Icons.chat;
      case ConversationType.mixed:
        return Icons.chat_bubble;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat('EEE').format(dateTime);
    } else {
      return DateFormat('MMM dd').format(dateTime);
    }
  }
}
