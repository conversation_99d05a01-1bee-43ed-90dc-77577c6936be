import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final bool showTimestamp;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const MessageBubble({
    super.key,
    required this.message,
    this.showTimestamp = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.role == MessageRole.user;
    final isSystem = message.role == MessageRole.system;
    
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 4,
      ),
      child: Column(
        crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          if (showTimestamp) _buildTimestamp(theme),
          Row(
            mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (!isUser && !isSystem) _buildAvatar(theme),
              Flexible(
                child: GestureDetector(
                  onTap: onTap,
                  onLongPress: onLongPress,
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    margin: EdgeInsets.only(
                      left: isUser ? 48 : 8,
                      right: isUser ? 8 : 48,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: _getBubbleColor(theme, isUser, isSystem),
                      borderRadius: _getBorderRadius(isUser),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildMessageContent(theme, isUser, isSystem),
                        if (message.type == MessageType.voice) _buildVoiceIndicator(theme),
                        _buildMessageStatus(theme, isUser),
                      ],
                    ),
                  ),
                ),
              ),
              if (isUser) _buildAvatar(theme),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Center(
        child: Text(
          DateFormat('MMM dd, HH:mm').format(message.timestamp),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme) {
    final isUser = message.role == MessageRole.user;
    
    return Container(
      width: 32,
      height: 32,
      margin: EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        color: isUser 
            ? theme.colorScheme.primary
            : theme.colorScheme.secondary,
        shape: BoxShape.circle,
      ),
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildMessageContent(ThemeData theme, bool isUser, bool isSystem) {
    final textColor = isUser 
        ? Colors.white
        : isSystem 
            ? theme.colorScheme.onSurface.withOpacity(0.7)
            : theme.colorScheme.onSurface;

    if (message.status == MessageStatus.typing) {
      return _buildTypingIndicator(theme);
    }

    return Text(
      message.content,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: textColor,
        height: 1.4,
      ),
    );
  }

  Widget _buildTypingIndicator(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildTypingDot(theme, 0),
        SizedBox(width: 4),
        _buildTypingDot(theme, 200),
        SizedBox(width: 4),
        _buildTypingDot(theme, 400),
      ],
    );
  }

  Widget _buildTypingDot(ThemeData theme, int delay) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.4, end: 1.0),
      duration: Duration(milliseconds: 600),
      builder: (context, value, child) {
        return AnimatedBuilder(
          animation: AlwaysStoppedAnimation(value),
          builder: (context, child) {
            return Transform.scale(
              scale: value,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVoiceIndicator(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.volume_up,
            size: 16,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          SizedBox(width: 4),
          if (message.audioDuration != null)
            Text(
              _formatDuration(message.audioDuration!),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessageStatus(ThemeData theme, bool isUser) {
    if (!isUser || message.status == MessageStatus.sent) return SizedBox.shrink();

    IconData icon;
    Color color;

    switch (message.status) {
      case MessageStatus.sending:
        icon = Icons.access_time;
        color = theme.colorScheme.onSurface.withOpacity(0.5);
        break;
      case MessageStatus.delivered:
        icon = Icons.done;
        color = theme.colorScheme.onSurface.withOpacity(0.5);
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = theme.colorScheme.error;
        break;
      default:
        return SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(top: 4),
      child: Align(
        alignment: Alignment.centerRight,
        child: Icon(
          icon,
          size: 14,
          color: color,
        ),
      ),
    );
  }

  Color _getBubbleColor(ThemeData theme, bool isUser, bool isSystem) {
    if (isSystem) {
      return theme.colorScheme.surfaceVariant.withOpacity(0.5);
    }
    
    return isUser 
        ? theme.colorScheme.primary
        : theme.colorScheme.surfaceVariant;
  }

  BorderRadius _getBorderRadius(bool isUser) {
    return BorderRadius.only(
      topLeft: Radius.circular(20),
      topRight: Radius.circular(20),
      bottomLeft: Radius.circular(isUser ? 20 : 4),
      bottomRight: Radius.circular(isUser ? 4 : 20),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }
}
