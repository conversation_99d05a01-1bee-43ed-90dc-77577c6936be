import 'package:flutter/material.dart';

class TypingIndicator extends StatefulWidget {
  final bool isVisible;
  final String? userName;
  final Color? backgroundColor;
  final Color? dotColor;

  const TypingIndicator({
    super.key,
    required this.isVisible,
    this.userName,
    this.backgroundColor,
    this.dotColor,
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(TypingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _startAnimation() {
    _fadeController.forward();
    _animationController.repeat();
  }

  void _stopAnimation() {
    _fadeController.reverse();
    _animationController.stop();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return SizedBox.shrink();
    }

    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            _buildAvatar(theme),
            SizedBox(width: 8),
            Flexible(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: widget.backgroundColor ?? 
                      theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.userName != null) _buildUserName(theme),
                    _buildTypingDots(theme),
                  ],
                ),
              ),
            ),
            SizedBox(width: 48), // Space for alignment
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar(ThemeData theme) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.smart_toy,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildUserName(ThemeData theme) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Text(
        widget.userName!,
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurface.withOpacity(0.7),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTypingDots(ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildAnimatedDot(theme, 0),
        SizedBox(width: 4),
        _buildAnimatedDot(theme, 0.2),
        SizedBox(width: 4),
        _buildAnimatedDot(theme, 0.4),
      ],
    );
  }

  Widget _buildAnimatedDot(ThemeData theme, double delay) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animationValue = (_animationController.value + delay) % 1.0;
        final scale = _calculateDotScale(animationValue);
        final opacity = _calculateDotOpacity(animationValue);
        
        return Transform.scale(
          scale: scale,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: (widget.dotColor ?? theme.colorScheme.onSurface)
                  .withOpacity(opacity),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  double _calculateDotScale(double animationValue) {
    if (animationValue < 0.5) {
      return 0.8 + (0.4 * animationValue * 2);
    } else {
      return 1.2 - (0.4 * (animationValue - 0.5) * 2);
    }
  }

  double _calculateDotOpacity(double animationValue) {
    if (animationValue < 0.5) {
      return 0.4 + (0.4 * animationValue * 2);
    } else {
      return 0.8 - (0.4 * (animationValue - 0.5) * 2);
    }
  }
}

class SimpleTypingIndicator extends StatelessWidget {
  final bool isVisible;
  final String text;

  const SimpleTypingIndicator({
    super.key,
    required this.isVisible,
    this.text = 'AI is typing...',
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return SizedBox.shrink();

    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
          ),
          SizedBox(width: 12),
          Text(
            text,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
}
