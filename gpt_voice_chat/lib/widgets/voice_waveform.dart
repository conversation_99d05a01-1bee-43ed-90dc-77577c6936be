import 'package:flutter/material.dart';
import 'dart:math' as math;

class VoiceWaveform extends StatefulWidget {
  final bool isActive;
  final double amplitude;
  final Color? color;
  final double height;
  final int barCount;

  const VoiceWaveform({
    super.key,
    required this.isActive,
    this.amplitude = 0.5,
    this.color,
    this.height = 40,
    this.barCount = 20,
  });

  @override
  State<VoiceWaveform> createState() => _VoiceWaveformState();
}

class _VoiceWaveformState extends State<VoiceWaveform>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _barControllers;
  late List<Animation<double>> _barAnimations;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: Duration(milliseconds: 2000),
      vsync: this,
    );

    _barControllers = List.generate(
      widget.barCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + (index * 50)),
        vsync: this,
      ),
    );

    _barAnimations = _barControllers.map((controller) {
      return Tween<double>(begin: 0.1, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    if (widget.isActive) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(VoiceWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _barControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimation() {
    _animationController.repeat();
    
    for (int i = 0; i < _barControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted && widget.isActive) {
          _barControllers[i].repeat(reverse: true);
        }
      });
    }
  }

  void _stopAnimation() {
    _animationController.stop();
    for (final controller in _barControllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = widget.color ?? theme.colorScheme.primary;

    return Container(
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: List.generate(widget.barCount, (index) {
          return AnimatedBuilder(
            animation: _barAnimations[index],
            builder: (context, child) {
              final barHeight = widget.isActive
                  ? widget.height * _barAnimations[index].value * widget.amplitude
                  : widget.height * 0.1;
              
              return Container(
                width: 3,
                height: barHeight,
                margin: EdgeInsets.symmetric(horizontal: 1),
                decoration: BoxDecoration(
                  color: color.withOpacity(widget.isActive ? 0.8 : 0.3),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

class CircularVoiceWaveform extends StatefulWidget {
  final bool isActive;
  final double amplitude;
  final Color? color;
  final double radius;
  final int waveCount;

  const CircularVoiceWaveform({
    super.key,
    required this.isActive,
    this.amplitude = 0.5,
    this.color,
    this.radius = 50,
    this.waveCount = 3,
  });

  @override
  State<CircularVoiceWaveform> createState() => _CircularVoiceWaveformState();
}

class _CircularVoiceWaveformState extends State<CircularVoiceWaveform>
    with TickerProviderStateMixin {
  late List<AnimationController> _waveControllers;
  late List<Animation<double>> _waveAnimations;

  @override
  void initState() {
    super.initState();
    
    _waveControllers = List.generate(
      widget.waveCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 1500 + (index * 200)),
        vsync: this,
      ),
    );

    _waveAnimations = _waveControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOut),
      );
    }).toList();

    if (widget.isActive) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(CircularVoiceWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _waveControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimation() {
    for (int i = 0; i < _waveControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 300), () {
        if (mounted && widget.isActive) {
          _waveControllers[i].repeat();
        }
      });
    }
  }

  void _stopAnimation() {
    for (final controller in _waveControllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = widget.color ?? theme.colorScheme.primary;

    return Container(
      width: widget.radius * 2,
      height: widget.radius * 2,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Animated wave circles
          ...List.generate(widget.waveCount, (index) {
            return AnimatedBuilder(
              animation: _waveAnimations[index],
              builder: (context, child) {
                final scale = _waveAnimations[index].value;
                final opacity = 1.0 - _waveAnimations[index].value;
                
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    width: widget.radius * 2,
                    height: widget.radius * 2,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: color.withOpacity(opacity * 0.5),
                        width: 2,
                      ),
                    ),
                  ),
                );
              },
            );
          }),
          
          // Center circle
          Container(
            width: widget.radius,
            height: widget.radius,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.mic,
              color: Colors.white,
              size: widget.radius * 0.4,
            ),
          ),
        ],
      ),
    );
  }
}

class VoiceLevelIndicator extends StatelessWidget {
  final double level; // 0.0 to 1.0
  final Color? color;
  final double width;
  final double height;

  const VoiceLevelIndicator({
    super.key,
    required this.level,
    this.color,
    this.width = 200,
    this.height = 8,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? theme.colorScheme.primary;

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(height / 2),
      ),
      child: Stack(
        children: [
          Container(
            width: width * level.clamp(0.0, 1.0),
            height: height,
            decoration: BoxDecoration(
              color: indicatorColor,
              borderRadius: BorderRadius.circular(height / 2),
            ),
          ),
        ],
      ),
    );
  }
}
