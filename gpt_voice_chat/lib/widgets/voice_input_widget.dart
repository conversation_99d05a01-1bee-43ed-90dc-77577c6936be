import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/voice_provider.dart';
import '../constants/app_constants.dart';

class VoiceInputWidget extends StatefulWidget {
  final Function(String) onTranscriptionComplete;
  final VoidCallback? onCancel;
  final String? conversationId;

  const VoiceInputWidget({
    super.key,
    required this.onTranscriptionComplete,
    this.onCancel,
    this.conversationId,
  });

  @override
  State<VoiceInputWidget> createState() => _VoiceInputWidgetState();
}

class _VoiceInputWidgetState extends State<VoiceInputWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));

    _startAnimations();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _waveController.repeat();
  }

  void _stopAnimations() {
    _pulseController.stop();
    _waveController.stop();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VoiceProvider>(
      builder: (context, voiceProvider, child) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              _buildHeader(context),
              Expanded(
                child: _buildContent(context, voiceProvider),
              ),
              _buildControls(context, voiceProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.mic,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: 12),
          Text(
            'Voice Input',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          Spacer(),
          IconButton(
            onPressed: () {
              _stopAnimations();
              widget.onCancel?.call();
            },
            icon: Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, VoiceProvider voiceProvider) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildVoiceVisualizer(theme, voiceProvider),
        SizedBox(height: 32),
        _buildStatusText(theme, voiceProvider),
        SizedBox(height: 16),
        _buildTranscriptionText(theme, voiceProvider),
      ],
    );
  }

  Widget _buildVoiceVisualizer(ThemeData theme, VoiceProvider voiceProvider) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _waveAnimation]),
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Outer wave circles
            for (int i = 0; i < 3; i++)
              Transform.scale(
                scale: _pulseAnimation.value + (i * 0.3),
                child: Container(
                  width: 120 + (i * 40),
                  height: 120 + (i * 40),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(
                        0.3 - (i * 0.1),
                      ),
                      width: 2,
                    ),
                  ),
                ),
              ),
            // Center microphone button
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: voiceProvider.isListening
                    ? theme.colorScheme.error
                    : theme.colorScheme.primary,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (voiceProvider.isListening
                            ? theme.colorScheme.error
                            : theme.colorScheme.primary)
                        .withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Icon(
                voiceProvider.isListening ? Icons.mic : Icons.mic_none,
                color: Colors.white,
                size: 32,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatusText(ThemeData theme, VoiceProvider voiceProvider) {
    String statusText;
    Color statusColor;

    switch (voiceProvider.state) {
      case VoiceState.listening:
        statusText = 'Listening...';
        statusColor = theme.colorScheme.primary;
        break;
      case VoiceState.processing:
        statusText = 'Processing...';
        statusColor = theme.colorScheme.secondary;
        break;
      case VoiceState.error:
        statusText = 'Error occurred';
        statusColor = theme.colorScheme.error;
        break;
      default:
        statusText = 'Tap to start listening';
        statusColor = theme.colorScheme.onSurface.withOpacity(0.6);
    }

    return Text(
      statusText,
      style: theme.textTheme.titleMedium?.copyWith(
        color: statusColor,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _buildTranscriptionText(ThemeData theme, VoiceProvider voiceProvider) {
    if (voiceProvider.recognizedText.isEmpty) {
      return Text(
        'Your speech will appear here...',
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.colorScheme.onSurface.withOpacity(0.5),
          fontStyle: FontStyle.italic,
        ),
        textAlign: TextAlign.center,
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        voiceProvider.recognizedText,
        style: theme.textTheme.bodyLarge,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildControls(BuildContext context, VoiceProvider voiceProvider) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Cancel button
          _buildControlButton(
            theme: theme,
            icon: Icons.close,
            label: 'Cancel',
            color: theme.colorScheme.error,
            onPressed: () {
              _stopAnimations();
              voiceProvider.cancelListening();
              widget.onCancel?.call();
            },
          ),
          
          // Main action button
          _buildMainActionButton(theme, voiceProvider),
          
          // Send button
          _buildControlButton(
            theme: theme,
            icon: Icons.send,
            label: 'Send',
            color: theme.colorScheme.primary,
            onPressed: voiceProvider.recognizedText.isNotEmpty
                ? () {
                    _stopAnimations();
                    widget.onTranscriptionComplete(voiceProvider.recognizedText);
                  }
                : null,
          ),
        ],
      ),
    );
  }

  Widget _buildMainActionButton(ThemeData theme, VoiceProvider voiceProvider) {
    IconData icon;
    String label;
    Color color;
    VoidCallback? onPressed;

    switch (voiceProvider.state) {
      case VoiceState.listening:
        icon = Icons.stop;
        label = 'Stop';
        color = theme.colorScheme.error;
        onPressed = () => voiceProvider.stopListening();
        break;
      case VoiceState.processing:
        icon = Icons.hourglass_empty;
        label = 'Processing';
        color = theme.colorScheme.secondary;
        onPressed = null;
        break;
      default:
        icon = Icons.mic;
        label = 'Listen';
        color = theme.colorScheme.primary;
        onPressed = () => voiceProvider.startListening();
    }

    return _buildControlButton(
      theme: theme,
      icon: icon,
      label: label,
      color: color,
      onPressed: onPressed,
      isLoading: voiceProvider.state == VoiceState.processing,
    );
  }

  Widget _buildControlButton({
    required ThemeData theme,
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 56,
          height: 56,
          child: ElevatedButton(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              shape: CircleBorder(),
              padding: EdgeInsets.zero,
            ),
            child: isLoading
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Icon(icon, size: 24),
          ),
        ),
        SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: onPressed != null
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
      ],
    );
  }
}
