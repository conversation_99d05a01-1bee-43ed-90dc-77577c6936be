import 'dart:math';
import '../models/models.dart';

class MockData {
  static final Random _random = Random();

  // Sample conversation titles
  static const List<String> _conversationTitles = [
    'Help with Flutter development',
    'Recipe for chocolate cake',
    'Travel planning for Japan',
    'Learning Spanish basics',
    'Workout routine suggestions',
    'Book recommendations',
    'Career advice discussion',
    'Home improvement ideas',
    'Photography tips',
    'Investment strategies',
    'Cooking techniques',
    'Movie recommendations',
    'Health and wellness',
    'Technology trends',
    'Creative writing help',
  ];

  // Sample AI responses
  static const List<String> _aiResponses = [
    "That's a great question! Let me help you with that.",
    "I understand what you're looking for. Here's my suggestion:",
    "Based on what you've told me, I think the best approach would be:",
    "That's an interesting perspective. Have you considered:",
    "I'd be happy to help you with that. Here are some options:",
    "That sounds like a common challenge. Here's what I recommend:",
    "Great idea! Let me expand on that thought:",
    "I see what you mean. Here's another way to think about it:",
    "That's a valid concern. Let me address that:",
    "Excellent question! The answer depends on a few factors:",
    "I can definitely help with that. Let's break it down:",
    "That's a smart approach. You might also want to consider:",
    "I appreciate you sharing that. Here's my take:",
    "That's a complex topic. Let me simplify it for you:",
    "Good thinking! Here's how you can take it further:",
  ];

  // Sample user messages
  static const List<String> _userMessages = [
    "How do I get started with Flutter?",
    "What's the best way to learn a new language?",
    "Can you help me plan a trip?",
    "I need advice on my career",
    "What should I cook for dinner?",
    "How can I improve my productivity?",
    "Tell me about the latest technology trends",
    "I'm looking for book recommendations",
    "How do I start investing?",
    "What's a good workout routine?",
    "Can you help me with my resume?",
    "I need creative writing inspiration",
    "How do I take better photos?",
    "What are some healthy meal ideas?",
    "Help me understand this concept",
  ];

  // Generate mock conversations
  static List<Conversation> generateMockConversations({int count = 10}) {
    final conversations = <Conversation>[];
    
    for (int i = 0; i < count; i++) {
      final conversation = Conversation(
        title: _conversationTitles[_random.nextInt(_conversationTitles.length)],
        messages: generateMockMessages(count: _random.nextInt(10) + 1),
        createdAt: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
        type: ConversationType.values[_random.nextInt(ConversationType.values.length)],
        isPinned: _random.nextBool() && i < 3, // Pin first few conversations randomly
      );
      conversations.add(conversation);
    }
    
    // Sort by most recent
    conversations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    return conversations;
  }

  // Generate mock messages for a conversation
  static List<Message> generateMockMessages({int count = 5}) {
    final messages = <Message>[];
    
    for (int i = 0; i < count; i++) {
      // Alternate between user and assistant messages
      final isUserMessage = i % 2 == 0;
      
      final message = Message(
        content: isUserMessage 
            ? _userMessages[_random.nextInt(_userMessages.length)]
            : _aiResponses[_random.nextInt(_aiResponses.length)],
        type: _random.nextBool() ? MessageType.text : MessageType.voice,
        role: isUserMessage ? MessageRole.user : MessageRole.assistant,
        timestamp: DateTime.now().subtract(Duration(minutes: (count - i) * 5)),
        status: MessageStatus.values[_random.nextInt(MessageStatus.values.length - 1)], // Exclude typing
      );
      
      messages.add(message);
    }
    
    return messages;
  }

  // Generate a single mock message
  static Message generateMockMessage({
    required MessageRole role,
    MessageType? type,
    String? content,
  }) {
    return Message(
      content: content ?? (role == MessageRole.user 
          ? _userMessages[_random.nextInt(_userMessages.length)]
          : _aiResponses[_random.nextInt(_aiResponses.length)]),
      type: type ?? MessageType.text,
      role: role,
      status: MessageStatus.sent,
    );
  }

  // Generate streaming response chunks
  static List<String> generateStreamingChunks(String fullResponse) {
    final words = fullResponse.split(' ');
    final chunks = <String>[];
    
    for (int i = 0; i < words.length; i++) {
      if (i == 0) {
        chunks.add(words[i]);
      } else {
        chunks.add(' ${words[i]}');
      }
    }
    
    return chunks;
  }

  // Generate mock API responses
  static ChatResponse generateMockChatResponse({
    required String conversationId,
    String? message,
  }) {
    return ChatResponse(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      message: message ?? _aiResponses[_random.nextInt(_aiResponses.length)],
      conversationId: conversationId,
      timestamp: DateTime.now(),
      isComplete: true,
    );
  }

  // Generate mock voice response
  static VoiceResponse generateMockVoiceResponse({
    required String conversationId,
    required String transcription,
  }) {
    return VoiceResponse(
      id: 'voice_${DateTime.now().millisecondsSinceEpoch}',
      transcription: transcription,
      response: _aiResponses[_random.nextInt(_aiResponses.length)],
      audioUrl: 'https://example.com/audio/${DateTime.now().millisecondsSinceEpoch}.mp3',
      conversationId: conversationId,
      timestamp: DateTime.now(),
      audioDuration: Duration(seconds: _random.nextInt(30) + 5),
    );
  }

  // Generate conversation list response
  static ConversationListResponse generateMockConversationListResponse({
    int page = 1,
    int limit = 20,
  }) {
    final conversations = generateMockConversations(count: limit);
    
    return ConversationListResponse(
      conversations: conversations.map((conv) => conv.toJson()).toList(),
      total: 100, // Mock total
      page: page,
      limit: limit,
    );
  }

  // Generate typing indicator message
  static Message generateTypingMessage() {
    return Message(
      content: '',
      type: MessageType.text,
      role: MessageRole.assistant,
      status: MessageStatus.typing,
    );
  }

  // Generate error message
  static Message generateErrorMessage(String error) {
    return Message(
      content: 'Sorry, I encountered an error: $error',
      type: MessageType.system,
      role: MessageRole.system,
      status: MessageStatus.failed,
    );
  }
}
