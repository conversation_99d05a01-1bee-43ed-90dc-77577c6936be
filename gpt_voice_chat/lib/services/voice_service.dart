import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/models.dart';
import 'mock_data.dart';

class VoiceService {
  static const String _baseUrl = 'https://api.example.com'; // Replace with actual API URL
  static const bool _useMockData = true; // Toggle for mock vs real API
  
  final http.Client _client = http.Client();
  final Random _random = Random();
  bool _isInitialized = false;
  bool _isSpeaking = false;

  // Initialize voice service
  Future<void> initialize() async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 500));
      _isInitialized = true;
      return;
    }

    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/voice/init'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        _isInitialized = true;
      } else {
        throw Exception('Failed to initialize voice service: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Voice service initialization error: $e');
    }
  }

  // Process voice message (speech-to-text + AI response + text-to-speech)
  Future<VoiceResponse> processVoiceMessage({
    required String text,
    required String conversationId,
  }) async {
    if (_useMockData) {
      // Simulate processing delay
      await Future.delayed(Duration(milliseconds: 1500 + _random.nextInt(1000)));
      return MockData.generateMockVoiceResponse(
        conversationId: conversationId,
        transcription: text,
      );
    }

    try {
      final request = VoiceRequest(
        audioData: '', // This would be base64 encoded audio in real implementation
        conversationId: conversationId,
      );

      final response = await _client.post(
        Uri.parse('$_baseUrl/voice/process'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return VoiceResponse.fromJson(data);
      } else {
        throw Exception('Failed to process voice message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Voice processing error: $e');
    }
  }

  // Convert text to speech
  Future<String> textToSpeech({
    required String text,
    String? voiceId,
    double? speed,
    double? pitch,
  }) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(500)));
      return 'https://example.com/audio/tts_${DateTime.now().millisecondsSinceEpoch}.mp3';
    }

    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/voice/tts'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'text': text,
          'voiceId': voiceId ?? 'default',
          'speed': speed ?? 1.0,
          'pitch': pitch ?? 1.0,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['audioUrl'];
      } else {
        throw Exception('Failed to convert text to speech: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Text-to-speech error: $e');
    }
  }

  // Convert speech to text
  Future<String> speechToText({
    required String audioData,
    String? language,
  }) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 800 + _random.nextInt(700)));
      return 'This is a mock transcription of the audio input.';
    }

    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/voice/stt'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'audioData': audioData,
          'language': language ?? 'en-US',
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['transcription'];
      } else {
        throw Exception('Failed to convert speech to text: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Speech-to-text error: $e');
    }
  }

  // Speak text (mock implementation)
  Future<void> speak(String text) async {
    if (_isSpeaking) {
      await stopSpeaking();
    }

    _isSpeaking = true;

    if (_useMockData) {
      // Simulate speaking duration based on text length
      final duration = Duration(milliseconds: text.length * 50 + 1000);
      await Future.delayed(duration);
      _isSpeaking = false;
      return;
    }

    try {
      // In a real implementation, this would use platform-specific TTS
      final audioUrl = await textToSpeech(text: text);
      
      // Play the audio (would use audio player in real implementation)
      await Future.delayed(Duration(seconds: 3)); // Mock playback duration
      
      _isSpeaking = false;
    } catch (e) {
      _isSpeaking = false;
      throw Exception('Speaking error: $e');
    }
  }

  // Stop speaking
  Future<void> stopSpeaking() async {
    _isSpeaking = false;
    
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 100));
      return;
    }

    try {
      // In real implementation, this would stop the audio player
      await Future.delayed(Duration(milliseconds: 100));
    } catch (e) {
      throw Exception('Stop speaking error: $e');
    }
  }

  // Get available voices
  Future<List<Map<String, dynamic>>> getAvailableVoices() async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 300));
      return [
        {'id': 'voice_1', 'name': 'Sarah', 'gender': 'female', 'language': 'en-US'},
        {'id': 'voice_2', 'name': 'John', 'gender': 'male', 'language': 'en-US'},
        {'id': 'voice_3', 'name': 'Emma', 'gender': 'female', 'language': 'en-GB'},
        {'id': 'voice_4', 'name': 'David', 'gender': 'male', 'language': 'en-GB'},
        {'id': 'voice_5', 'name': 'Maria', 'gender': 'female', 'language': 'es-ES'},
      ];
    }

    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/voice/voices'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['voices']);
      } else {
        throw Exception('Failed to get available voices: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Get voices error: $e');
    }
  }

  // Start voice conversation (WebSocket connection)
  Stream<Map<String, dynamic>> startVoiceConversation({
    required String conversationId,
  }) async* {
    if (_useMockData) {
      // Mock voice conversation events
      yield {'type': 'connected', 'message': 'Voice conversation started'};
      
      await Future.delayed(Duration(seconds: 2));
      yield {'type': 'listening', 'message': 'Listening for your voice...'};
      
      await Future.delayed(Duration(seconds: 3));
      yield {'type': 'transcription', 'text': 'Hello, how can I help you today?'};
      
      await Future.delayed(Duration(seconds: 1));
      yield {'type': 'response', 'text': 'Hello! I\'m here to help. What would you like to talk about?'};
      
      await Future.delayed(Duration(seconds: 2));
      yield {'type': 'speaking', 'message': 'Playing AI response...'};
      
      return;
    }

    try {
      // In real implementation, this would establish a WebSocket connection
      // for real-time voice communication
      final uri = Uri.parse('wss://api.example.com/voice/conversation/$conversationId');
      
      // WebSocket implementation would go here
      // For now, we'll simulate with mock data
      yield {'type': 'error', 'message': 'WebSocket not implemented yet'};
    } catch (e) {
      yield {'type': 'error', 'message': 'Voice conversation error: $e'};
    }
  }

  // End voice conversation
  Future<void> endVoiceConversation() async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 200));
      return;
    }

    try {
      // Close WebSocket connection and cleanup
      await Future.delayed(Duration(milliseconds: 200));
    } catch (e) {
      throw Exception('End voice conversation error: $e');
    }
  }

  // Check if voice service is available
  bool get isAvailable => _isInitialized;
  
  // Check if currently speaking
  bool get isSpeaking => _isSpeaking;

  void dispose() {
    _client.close();
  }
}
