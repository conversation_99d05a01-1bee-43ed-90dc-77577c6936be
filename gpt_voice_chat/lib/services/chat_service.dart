import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/models.dart';
import 'mock_data.dart';

class ChatService {
  static const String _baseUrl = 'https://api.example.com'; // Replace with actual API URL
  static const bool _useMockData = true; // Toggle for mock vs real API
  
  final http.Client _client = http.Client();
  final Random _random = Random();

  // Get all conversations
  Future<List<Conversation>> getConversations() async {
    if (_useMockData) {
      // Simulate network delay
      await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));
      return MockData.generateMockConversations(count: 15);
    }

    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/conversations'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final conversationList = ConversationListResponse.fromJson(data);
        
        return conversationList.conversations
            .map((convJson) => Conversation.fromJson(convJson))
            .toList();
      } else {
        throw Exception('Failed to load conversations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Create a new conversation
  Future<Conversation> createConversation({required String title}) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(500)));
      return Conversation(title: title);
    }

    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/conversations'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'title': title}),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Conversation.fromJson(data['conversation']);
      } else {
        throw Exception('Failed to create conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Send a message and get response
  Future<ChatResponse> sendMessage({
    required String message,
    required String conversationId,
  }) async {
    if (_useMockData) {
      // Simulate typing delay
      await Future.delayed(Duration(milliseconds: 1000 + _random.nextInt(2000)));
      return MockData.generateMockChatResponse(
        conversationId: conversationId,
        message: _generateMockResponse(message),
      );
    }

    try {
      final request = ChatRequest(
        message: message,
        conversationId: conversationId,
      );

      final response = await _client.post(
        Uri.parse('$_baseUrl/chat'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ChatResponse.fromJson(data);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Send a streaming message
  Stream<String> sendStreamingMessage({
    required String message,
    required String conversationId,
  }) async* {
    if (_useMockData) {
      final fullResponse = _generateMockResponse(message);
      final chunks = MockData.generateStreamingChunks(fullResponse);
      
      for (final chunk in chunks) {
        await Future.delayed(Duration(milliseconds: 50 + _random.nextInt(150)));
        yield chunk;
      }
      return;
    }

    try {
      final request = ChatRequest(
        message: message,
        conversationId: conversationId,
        stream: true,
      );

      final response = await _client.post(
        Uri.parse('$_baseUrl/chat/stream'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final stream = response.stream.transform(utf8.decoder);
        await for (final chunk in stream) {
          // Parse server-sent events or streaming JSON
          if (chunk.isNotEmpty) {
            yield chunk;
          }
        }
      } else {
        throw Exception('Failed to start streaming: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Streaming error: $e');
    }
  }

  // Update conversation
  Future<void> updateConversation(Conversation conversation) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 200 + _random.nextInt(300)));
      return;
    }

    try {
      final response = await _client.put(
        Uri.parse('$_baseUrl/conversations/${conversation.id}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(conversation.toJson()),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to update conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Delete conversation
  Future<void> deleteConversation(String conversationId) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(400)));
      return;
    }

    try {
      final response = await _client.delete(
        Uri.parse('$_baseUrl/conversations/$conversationId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get conversation by ID
  Future<Conversation> getConversation(String conversationId) async {
    if (_useMockData) {
      await Future.delayed(Duration(milliseconds: 300 + _random.nextInt(500)));
      final conversations = MockData.generateMockConversations(count: 1);
      return conversations.first.copyWith(id: conversationId);
    }

    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/conversations/$conversationId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Conversation.fromJson(data['conversation']);
      } else {
        throw Exception('Failed to get conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Generate contextual mock responses
  String _generateMockResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    if (message.contains('flutter') || message.contains('dart')) {
      return "Flutter is a great framework for cross-platform development! Here are some key concepts to get you started: widgets, state management, and the widget tree. Would you like me to explain any of these in more detail?";
    } else if (message.contains('recipe') || message.contains('cook')) {
      return "I'd be happy to help with cooking! For a delicious chocolate cake, you'll need flour, sugar, cocoa powder, eggs, and butter. The key is to not overmix the batter. Would you like the full recipe with step-by-step instructions?";
    } else if (message.contains('travel') || message.contains('trip')) {
      return "Travel planning can be exciting! I recommend starting with your budget, preferred dates, and must-see destinations. For Japan, consider visiting during spring for cherry blossoms or fall for beautiful foliage. What type of experience are you looking for?";
    } else if (message.contains('learn') || message.contains('study')) {
      return "Learning new skills is wonderful! The key is consistency and practice. I suggest breaking down your goal into smaller, manageable steps and setting a regular schedule. What specifically would you like to learn?";
    } else {
      // Return a generic helpful response
      final responses = [
        "That's an interesting question! Let me think about the best way to help you with that.",
        "I understand what you're asking. Here's my perspective on this topic:",
        "Great question! There are several approaches we could take here.",
        "I'd be happy to help you explore this further. Let me share some insights:",
        "That's a thoughtful inquiry. Based on what you've shared, I think:",
      ];
      return responses[_random.nextInt(responses.length)];
    }
  }

  void dispose() {
    _client.close();
  }
}
